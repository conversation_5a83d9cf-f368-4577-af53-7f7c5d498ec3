<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

class StoreOpenOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Adjust authorization logic if needed
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'categories' => 'nullable|array',
            'categories.*' => 'string|max:255',
            'budget' => 'nullable|string|max:255',
            'deadline' => 'nullable|date',
            'company' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'description' => 'required|string',
            'files' => 'nullable|array',
            'files.*' => 'file|max:2048',
            'recruitment_type' => 'required|in:company,personal',
            'open_to_applications' => 'boolean',
            'auto_invite' => 'boolean',
            'status' => 'in:pending,open,closed,in_progress,completed,invited', // Add 'pending' and 'invited' if you use them

            // Filter Criteria Rules
            'filters' => 'nullable|array',
            'filters.languages' => 'nullable|array',
            'filters.languages.*' => 'string|max:255', // Assuming language is a string
            'filters.skills' => 'nullable|array',
            'filters.skills.*' => 'string|max:255', // Assuming skill is a string
            'filters.location' => 'nullable|string|max:255', // Example: City or Country
            'filters.experience_years' => 'nullable|integer|min:0',
            'filters.availability_status' => 'nullable|in:available,unavailable', // Or use constants from FreelanceProfile model
        ];
    }

    /**
     * Custom message for specific rules
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Le titre de l\'offre est obligatoire.',
            'title.max' => 'Le titre de l\'offre ne doit pas dépasser 255 caractères.',
            'categories.*.max' => 'Chaque catégorie ne doit pas dépasser 255 caractères.',
            'budget.max' => 'Le budget ne doit pas dépasser 255 caractères.',
            'deadline.date' => 'La date limite doit être une date valide.',
            'company.max' => 'Le nom de l\'entreprise ne doit pas dépasser 255 caractères.',
            'website.url' => 'L\'URL du site web doit être une URL valide.',
            'website.max' => 'L\'URL du site web ne doit pas dépasser 255 caractères.',
            'description.required' => 'La description de l\'offre est obligatoire.',
            'files.*.file' => 'Le fichier doit être un fichier valide.',
            'files.*.max' => 'Chaque fichier ne doit pas dépasser 2048 Ko.',
            'recruitment_type.required' => 'Le type de recrutement est obligatoire.',
            'recruitment_type.in' => 'Le type de recrutement doit être "company" ou "personal".',
            'status.in' => 'Le statut doit être parmi les valeurs suivantes : pending, open, closed, in_progress, completed, invited.',
            'filters.languages.*.max' => 'Chaque langue ne doit pas dépasser 255 caractères.',
            'filters.skills.*.max' => 'Chaque compétence ne doit pas dépasser 255 caractères.',
            'filters.location.max' => 'La localisation ne doit pas dépasser 255 caractères.',
            'filters.experience_years.integer' => 'Les années d\'expérience doivent être un nombre entier.',
            'filters.experience_years.min' => 'Les années d\'expérience doivent être un nombre positif ou nul.',
            'filters.availability_status.in' => 'Le statut de disponibilité doit être "available" ou "unavailable".',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(new JsonResponse([
            'errors' => $validator->errors(),
        ], 422));
    }
}
