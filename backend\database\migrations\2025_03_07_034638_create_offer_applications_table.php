<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offer_applications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('open_offer_id')->constrained()->onDelete('cascade');
            $table->foreignId('freelance_profile_id')->constrained()->onDelete('cascade');
            $table->text('proposal')->nullable();
            $table->enum('status', ['pending', 'accepted', 'rejected', 'invited'])->default('pending');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offer_applications');
    }
};
